<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>@yield('title', 'Tanzania Center of Global Ventures - Your Gateway To a World of Opportunities')</title>
    <meta name="description" content="@yield('description', 'Tanzania Center of Global Ventures offers Study Abroad, Ticketing Service, Visa Application, Work Abroad, and Travel & Tours services.')">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=inter:400,500,600,700&display=swap" rel="stylesheet" />

    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Styles / Scripts -->
    @if (file_exists(public_path('build/manifest.json')) || file_exists(public_path('hot')))
        @vite(['resources/css/app.css', 'resources/js/app.js'])
    @else
        <style>
            /* Tailwind CSS will be included here */
        </style>
    @endif

    <style>
        body { font-family: 'Inter', sans-serif; }
        .bg-primary { background-color: #1e40af; }
        .bg-primary-dark { background-color: #1e3a8a; }
        .text-primary { color: #1e40af; }
        .text-secondary { color: #dc2626; }
        .border-primary { border-color: #1e40af; }
        .hover\:bg-primary:hover { background-color: #1e40af; }
        .hover\:text-primary:hover { color: #1e40af; }
    </style>
</head>
<body class="bg-gray-50">


    <!-- Main Header -->
    <header class="bg-slate-900 shadow-2xl sticky top-0 z-50 border-b-4 border-orange-500">
        <div class="container mx-auto px-4">
            <div class="flex items-center justify-between py-4">
                <!-- Logo -->
                <div class="flex items-center space-x-4">
                    <div class="relative">
                        <img src="{{ asset('images/logo.png') }}" alt="Global Ventures Logo" class="h-14 w-auto">
                        <div class="absolute -top-1 -right-1 w-4 h-4 bg-orange-500 rounded-full animate-pulse"></div>
                    </div>
                    <div class="text-white">
                        <h1 class="text-xl font-black leading-tight tracking-wide">GLOBAL VENTURES</h1>
                        <p class="text-sm text-orange-300 leading-tight font-semibold">Tanzania Center</p>
                    </div>
                </div>

                <!-- Desktop Navigation -->
                <nav class="hidden lg:flex items-center space-x-2">
                    <a href="{{ route('home') }}" class="text-gray-300 hover:text-orange-400 font-semibold transition-all duration-300 px-4 py-2 rounded-full border-2 border-transparent hover:border-orange-400 {{ request()->routeIs('home') ? 'bg-orange-500 text-white border-orange-500' : '' }}">Home</a>
                    <a href="{{ route('about') }}" class="text-gray-300 hover:text-orange-400 font-semibold transition-all duration-300 px-4 py-2 rounded-full border-2 border-transparent hover:border-orange-400 {{ request()->routeIs('about') ? 'bg-orange-500 text-white border-orange-500' : '' }}">About</a>
                    <a href="{{ route('services') }}" class="text-gray-300 hover:text-orange-400 font-semibold transition-all duration-300 px-4 py-2 rounded-full border-2 border-transparent hover:border-orange-400 {{ request()->routeIs('services') ? 'bg-orange-500 text-white border-orange-500' : '' }}">Services</a>
                    <a href="{{ route('contact') }}" class="text-gray-300 hover:text-orange-400 font-semibold transition-all duration-300 px-4 py-2 rounded-full border-2 border-transparent hover:border-orange-400 {{ request()->routeIs('contact') ? 'bg-orange-500 text-white border-orange-500' : '' }}">Contact</a>
                </nav>

                <!-- Mobile Menu Button -->
                <button class="lg:hidden text-gray-300 hover:text-orange-400 p-2 rounded-lg border-2 border-gray-600 hover:border-orange-400 transition-all" id="mobile-menu-button">
                    <i class="fas fa-bars text-xl"></i>
                </button>
            </div>

            <!-- Mobile Navigation -->
            <nav class="lg:hidden hidden" id="mobile-menu">
                <div class="py-4 border-t border-orange-500/30">
                    <a href="{{ route('home') }}" class="block py-3 px-4 text-gray-300 hover:text-orange-400 font-semibold rounded-lg mx-2 my-1 transition-all {{ request()->routeIs('home') ? 'text-white bg-orange-500' : 'hover:bg-gray-800' }}">Home</a>
                    <a href="{{ route('about') }}" class="block py-3 px-4 text-gray-300 hover:text-orange-400 font-semibold rounded-lg mx-2 my-1 transition-all {{ request()->routeIs('about') ? 'text-white bg-orange-500' : 'hover:bg-gray-800' }}">About</a>
                    <a href="{{ route('services') }}" class="block py-3 px-4 text-gray-300 hover:text-orange-400 font-semibold rounded-lg mx-2 my-1 transition-all {{ request()->routeIs('services') ? 'text-white bg-orange-500' : 'hover:bg-gray-800' }}">Services</a>
                    <a href="{{ route('contact') }}" class="block py-3 px-4 text-gray-300 hover:text-orange-400 font-semibold rounded-lg mx-2 my-1 transition-all {{ request()->routeIs('contact') ? 'text-white bg-orange-500' : 'hover:bg-gray-800' }}">Contact</a>
                </div>
            </nav>
        </div>
    </header>

    <!-- Main Content -->
    <main>
        @yield('content')
    </main>

    <!-- Footer -->
    <footer class="bg-slate-900 text-white border-t-4 border-orange-500">
        <div class="container mx-auto px-4 py-12">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                <!-- Company Info -->
                <div>
                    <div class="flex items-center mb-4">
                        <img src="{{ asset('images/logo.png') }}" alt="Global Ventures Logo" class="h-10 w-auto mr-3">
                        <div>
                            <h3 class="text-lg font-bold">Global Ventures</h3>
                            <p class="text-sm text-gray-400">Your Gateway To Opportunities</p>
                        </div>
                    </div>
                    <p class="text-gray-400 text-sm mb-4">We are a Travel Agency dealing with Air Ticketing, Study/Jobs Abroad, & Tours</p>
                </div>

                <!-- Useful Links -->
                <div>
                    <h4 class="text-lg font-semibold mb-4">Useful Links</h4>
                    <ul class="space-y-2">
                        <li><a href="{{ route('services') }}" class="text-gray-400 hover:text-white transition-colors">Services</a></li>
                        <li><a href="{{ route('about') }}" class="text-gray-400 hover:text-white transition-colors">About Us</a></li>
                        <li><a href="{{ route('contact') }}" class="text-gray-400 hover:text-white transition-colors">Contact Us</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Gallery</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Travel</a></li>
                    </ul>
                </div>

                <!-- Newsletter -->
                <div>
                    <h4 class="text-lg font-semibold mb-4">Newsletter</h4>
                    <p class="text-gray-400 text-sm mb-4">Subscribe to get Latest News, Offers and connect With Us.</p>
                    <form class="flex">
                        <input type="email" placeholder="Enter Email Address" class="flex-1 px-3 py-2 bg-gray-800 text-white rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <button type="submit" class="bg-primary hover:bg-primary-dark px-4 py-2 rounded-r-md transition-colors">
                            Subscribe
                        </button>
                    </form>
                </div>

                <!-- Contact Info -->
                <div>
                    <h4 class="text-lg font-semibold mb-4 uppercase tracking-wide">Contact Us</h4>
                    <div class="space-y-3 text-sm text-gray-400">
                        <div class="flex items-start">
                            <i class="fas fa-map-marker-alt mt-1 mr-3 text-orange-500"></i>
                            <p>Victoria House Building, New Bagamoyo Road, 8th Floor, Wing B, Office 04 | Dar es salaam | Tanzania</p>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-phone mr-3 text-orange-500"></i>
                            <a href="tel:+255696303898" class="hover:text-white transition-colors">+255 696 303 898</a>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-envelope mr-3 text-orange-500"></i>
                            <a href="mailto:<EMAIL>" class="hover:text-white transition-colors"><EMAIL></a>
                        </div>
                        <div class="flex items-start">
                            <i class="fas fa-clock mt-1 mr-3 text-orange-500"></i>
                            <div>
                                <p>Mon-Fri: 8:00 AM - 6:00 PM</p>
                                <p>Saturday: 9:00 AM - 4:00 PM</p>
                                <p>Sunday: Closed</p>
                            </div>
                        </div>
                    </div>

                    <!-- Social Media -->
                    <div class="mt-6">
                        <h5 class="text-sm font-semibold mb-3 uppercase tracking-wide">Follow Us</h5>
                        <div class="flex space-x-3">
                            <a href="#" class="bg-slate-800 hover:bg-orange-500 text-gray-400 hover:text-white w-8 h-8 flex items-center justify-center transition-colors border border-gray-700 hover:border-orange-500">
                                <i class="fab fa-facebook-f text-sm"></i>
                            </a>
                            <a href="#" class="bg-slate-800 hover:bg-orange-500 text-gray-400 hover:text-white w-8 h-8 flex items-center justify-center transition-colors border border-gray-700 hover:border-orange-500">
                                <i class="fab fa-twitter text-sm"></i>
                            </a>
                            <a href="#" class="bg-slate-800 hover:bg-orange-500 text-gray-400 hover:text-white w-8 h-8 flex items-center justify-center transition-colors border border-gray-700 hover:border-orange-500">
                                <i class="fab fa-instagram text-sm"></i>
                            </a>
                            <a href="#" class="bg-slate-800 hover:bg-orange-500 text-gray-400 hover:text-white w-8 h-8 flex items-center justify-center transition-colors border border-gray-700 hover:border-orange-500">
                                <i class="fab fa-linkedin-in text-sm"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400 text-sm">
                <p>&copy; {{ date('Y') }} Tanzania Center of Global Ventures. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Mobile Menu Script -->
    <script>
        document.getElementById('mobile-menu-button').addEventListener('click', function() {
            const mobileMenu = document.getElementById('mobile-menu');
            mobileMenu.classList.toggle('hidden');
        });
    </script>
</body>
</html>

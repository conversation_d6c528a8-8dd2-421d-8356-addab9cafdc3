<?php

use Illuminate\Support\Facades\Route;

// Home page
Route::get('/', function () {
    return view('home');
})->name('home');

// About Us page
Route::get('/about', function () {
    return view('about');
})->name('about');

// Services page
Route::get('/services', function () {
    return view('services');
})->name('services');

// Contact page
Route::get('/contact', function () {
    return view('contact');
})->name('contact');

// Contact form submission
Route::post('/contact', function () {
    // Handle contact form submission here
    return redirect()->route('contact')->with('success', 'Thank you for your message! We will get back to you soon.');
})->name('contact.submit');
